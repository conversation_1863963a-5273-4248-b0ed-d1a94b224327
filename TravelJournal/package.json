{"name": "travel-journal", "version": "1.0.0", "description": "A comprehensive mobile travel journal application", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@bam.tech/react-native-image-resizer": "^3.0.0", "@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "~2.2.0", "@react-navigation/bottom-tabs": "^7.4.0", "@react-navigation/native": "^7.1.0", "@react-navigation/stack": "^7.4.0", "@reduxjs/toolkit": "^2.8.0", "decode-uri-component": "^0.4.1", "expo": "~52.0.0", "expo-av": "~15.1.0", "expo-camera": "~16.1.0", "expo-file-system": "~18.1.0", "expo-image-manipulator": "~13.1.0", "expo-image-picker": "~16.1.0", "expo-location": "~18.1.0", "expo-media-library": "~17.1.0", "expo-secure-store": "~14.2.0", "expo-sharing": "~13.1.0", "expo-sqlite": "~15.2.0", "expo-status-bar": "~2.2.0", "react": "19.1.0", "react-is": "^19.1.0", "react-native": "0.80.0", "react-native-gesture-handler": "~2.26.0", "react-native-google-places-autocomplete": "^2.5.0", "react-native-maps": "1.24.3", "react-native-paper": "^5.10.0", "react-native-reanimated": "~3.18.0", "react-native-safe-area-context": "~5.5.0", "react-native-screens": "~4.11.0", "react-native-svg": "15.12.0", "react-native-vector-icons": "^10.0.0", "react-native-view-shot": "^4.0.3", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@testing-library/react-native": "^13.0.0", "@types/react": "~19.1.0", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "9.29.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "5.2.0", "jest": "^30.0.1", "prettier": "^3.0.0", "typescript": "^5.1.3"}, "keywords": ["expo", "react-native", "typescript", "travel", "journal", "mobile"], "author": "Travel Journal Team", "license": "MIT", "private": true}